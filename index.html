<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> - AI Engineer & SAP Expert | VIBE GUY AI</title>
    <meta name="description" content="<PERSON><PERSON> Engineer, SAP Architect, and Cloud Expert with 10+ years of experience in enterprise digital transformation.">
    <meta name="keywords" content="AI Engineer, SAP Expert, Cloud Architect, Digital Transformation, S/4HANA, Azure">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#0066cc">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="style.css" as="style">
    <link rel="preload" href="script.js" as="script">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="style.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">AMIT LAL</span>
                <span class="logo-subtitle">VIBE GUY AI</span>
            </div>
            
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item"><a href="#home" class="nav-link">Home</a></li>
                <li class="nav-item"><a href="#about" class="nav-link">About</a></li>
                <li class="nav-item"><a href="#projects" class="nav-link">Projects</a></li>
                <li class="nav-item"><a href="#publications" class="nav-link">Publications</a></li>
                <li class="nav-item"><a href="#experience" class="nav-link">Experience</a></li>
                <li class="nav-item"><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            
            <div class="nav-controls">
                <button class="search-btn" id="search-btn" aria-label="Search">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="m21 21-4.35-4.35"></path>
                    </svg>
                </button>
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <svg class="sun-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                    <svg class="moon-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                    </svg>
                </button>
                <button class="hamburger" id="hamburger" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-background">
            <div class="blob blob-1"></div>
            <div class="blob blob-2"></div>
            <div class="blob blob-3"></div>
        </div>
        
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-image">
                    <img src="https://via.placeholder.com/200x200/0066cc/ffffff?text=AL" alt="Amit Lal" class="profile-img">
                    <div class="image-glow"></div>
                </div>
                
                <h1 class="hero-title">
                    Hi, I'm <span class="highlight">Amit Lal</span>
                </h1>
                
                <div class="hero-subtitle">
                    <span class="typewriter" id="typewriter"></span>
                    <span class="cursor">|</span>
                </div>
                
                <p class="hero-description">
                    Transforming enterprises through AI innovation and SAP excellence. 
                    10+ years of experience in cloud architecture and digital transformation.
                </p>
                
                <div class="hero-cta">
                    <a href="#projects" class="btn btn-primary">View My Work</a>
                    <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                </div>
                
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">10+</span>
                        <span class="stat-label">Years Experience</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">50+</span>
                        <span class="stat-label">Projects Delivered</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">8</span>
                        <span class="stat-label">Publications</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Me</h2>
                <p class="section-subtitle">Passionate about bridging AI innovation with enterprise solutions</p>
            </div>
            
            <div class="about-content">
                <div class="about-text">
                    <p class="about-intro">
                        I'm an AI Engineer and SAP Expert specializing in enterprise digital transformation. 
                        With over a decade of experience, I help organizations leverage cutting-edge AI technologies 
                        while optimizing their SAP ecosystems for maximum efficiency and innovation.
                    </p>
                    
                    <div class="expertise-areas">
                        <div class="expertise-item">
                            <h3>AI & Machine Learning</h3>
                            <p>Developing intelligent solutions that automate processes and provide actionable insights.</p>
                        </div>
                        <div class="expertise-item">
                            <h3>SAP Architecture</h3>
                            <p>S/4HANA migrations, cloud integrations, and enterprise system optimization.</p>
                        </div>
                        <div class="expertise-item">
                            <h3>Cloud Solutions</h3>
                            <p>Azure and AWS cloud architecture for scalable, secure enterprise applications.</p>
                        </div>
                    </div>
                </div>
                
                <div class="skills-grid">
                    <div class="skill-category">
                        <h3>AI & Data Science</h3>
                        <div class="skills">
                            <div class="skill-item">
                                <span class="skill-name">Python</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="95"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">TensorFlow</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="90"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Azure AI</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="88"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h3>SAP Technologies</h3>
                        <div class="skills">
                            <div class="skill-item">
                                <span class="skill-name">S/4HANA</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="95"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">SAP Cloud</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="92"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">ABAP</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="85"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h3>Cloud & DevOps</h3>
                        <div class="skills">
                            <div class="skill-item">
                                <span class="skill-name">Azure</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="90"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Docker</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="88"></div>
                                </div>
                            </div>
                            <div class="skill-item">
                                <span class="skill-name">Kubernetes</span>
                                <div class="skill-bar">
                                    <div class="skill-progress" data-width="82"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Modal -->
    <div class="search-modal" id="search-modal">
        <div class="search-modal-content">
            <div class="search-input-container">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                </svg>
                <input type="text" class="search-input" placeholder="Search..." id="search-input">
                <button class="search-close" id="search-close">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="search-results" id="search-results"></div>
        </div>
    </div>

    <!-- Projects Section -->
    <section class="projects" id="projects">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Projects</h2>
                <p class="section-subtitle">Innovative solutions that drive digital transformation</p>
            </div>

            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                            <line x1="12" y1="22.08" x2="12" y2="12"></line>
                        </svg>
                    </div>
                    <h3>SAP S/4HANA Cloud Integration</h3>
                    <p>Complete migration and integration of legacy SAP systems to S/4HANA Cloud, improving performance by 40% and reducing operational costs.</p>
                    <div class="project-tech">
                        <span class="tech-tag">S/4HANA</span>
                        <span class="tech-tag">Cloud</span>
                        <span class="tech-tag">Integration</span>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                    </div>
                    <h3>AI-Powered Enterprise Chatbot</h3>
                    <p>Intelligent chatbot using NLP and machine learning to handle 80% of customer inquiries automatically, integrated with SAP CRM.</p>
                    <div class="project-tech">
                        <span class="tech-tag">Python</span>
                        <span class="tech-tag">NLP</span>
                        <span class="tech-tag">Azure AI</span>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <path d="M9 9h6v6H9z"></path>
                        </svg>
                    </div>
                    <h3>Real-time Analytics Dashboard</h3>
                    <p>Executive dashboard providing real-time insights from multiple SAP modules with predictive analytics and automated reporting.</p>
                    <div class="project-tech">
                        <span class="tech-tag">Power BI</span>
                        <span class="tech-tag">SAP HANA</span>
                        <span class="tech-tag">Analytics</span>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                    </div>
                    <h3>Automated Invoice Processing</h3>
                    <p>AI-driven invoice processing system using OCR and machine learning to automate AP workflows, reducing processing time by 75%.</p>
                    <div class="project-tech">
                        <span class="tech-tag">OCR</span>
                        <span class="tech-tag">ML</span>
                        <span class="tech-tag">Automation</span>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                    </div>
                    <h3>Multi-tenant SaaS Platform</h3>
                    <p>Scalable SaaS platform built on Azure with microservices architecture, serving 10,000+ users across multiple industries.</p>
                    <div class="project-tech">
                        <span class="tech-tag">Azure</span>
                        <span class="tech-tag">Microservices</span>
                        <span class="tech-tag">SaaS</span>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"></path>
                            <path d="M12 15l-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"></path>
                            <path d="M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0"></path>
                            <path d="M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5"></path>
                        </svg>
                    </div>
                    <h3>Azure Microservices Architecture</h3>
                    <p>Enterprise-grade microservices architecture on Azure with containerization, API management, and automated CI/CD pipelines.</p>
                    <div class="project-tech">
                        <span class="tech-tag">Docker</span>
                        <span class="tech-tag">Kubernetes</span>
                        <span class="tech-tag">DevOps</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Publications Section -->
    <section class="publications" id="publications">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Publications & Insights</h2>
                <p class="section-subtitle">Sharing knowledge and best practices with the community</p>
            </div>

            <div class="publications-grid">
                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Whitepaper</span>
                        <span class="publication-date">2024</span>
                    </div>
                    <h3>AI Integration in SAP Ecosystems</h3>
                    <p>Comprehensive guide on integrating AI capabilities into existing SAP landscapes for enhanced business intelligence.</p>
                    <div class="publication-tags">
                        <span class="tag">AI</span>
                        <span class="tag">SAP</span>
                        <span class="tag">Integration</span>
                    </div>
                </div>

                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Technical Guide</span>
                        <span class="publication-date">2024</span>
                    </div>
                    <h3>S/4HANA Migration Best Practices</h3>
                    <p>Step-by-step methodology for successful S/4HANA migrations with minimal business disruption and maximum ROI.</p>
                    <div class="publication-tags">
                        <span class="tag">S/4HANA</span>
                        <span class="tag">Migration</span>
                        <span class="tag">Best Practices</span>
                    </div>
                </div>

                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Research Paper</span>
                        <span class="publication-date">2023</span>
                    </div>
                    <h3>Cloud-Native Enterprise Architecture</h3>
                    <p>Analysis of cloud-native patterns and their application in enterprise environments for improved scalability.</p>
                    <div class="publication-tags">
                        <span class="tag">Cloud</span>
                        <span class="tag">Architecture</span>
                        <span class="tag">Enterprise</span>
                    </div>
                </div>

                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Case Study</span>
                        <span class="publication-date">2023</span>
                    </div>
                    <h3>Digital Transformation Success Stories</h3>
                    <p>Real-world case studies of successful digital transformation initiatives across various industries.</p>
                    <div class="publication-tags">
                        <span class="tag">Digital Transformation</span>
                        <span class="tag">Case Study</span>
                    </div>
                </div>

                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Technical Article</span>
                        <span class="publication-date">2023</span>
                    </div>
                    <h3>Microservices in SAP Environments</h3>
                    <p>Implementing microservices architecture patterns within SAP landscapes for improved modularity and maintainability.</p>
                    <div class="publication-tags">
                        <span class="tag">Microservices</span>
                        <span class="tag">SAP</span>
                        <span class="tag">Architecture</span>
                    </div>
                </div>

                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Industry Report</span>
                        <span class="publication-date">2022</span>
                    </div>
                    <h3>Future of Enterprise AI</h3>
                    <p>Comprehensive analysis of emerging AI trends and their potential impact on enterprise software development.</p>
                    <div class="publication-tags">
                        <span class="tag">AI</span>
                        <span class="tag">Enterprise</span>
                        <span class="tag">Future Trends</span>
                    </div>
                </div>

                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Technical Guide</span>
                        <span class="publication-date">2022</span>
                    </div>
                    <h3>Azure DevOps for SAP Projects</h3>
                    <p>Complete guide to implementing DevOps practices in SAP development using Azure DevOps tools and methodologies.</p>
                    <div class="publication-tags">
                        <span class="tag">DevOps</span>
                        <span class="tag">Azure</span>
                        <span class="tag">SAP</span>
                    </div>
                </div>

                <div class="publication-card">
                    <div class="publication-meta">
                        <span class="publication-type">Whitepaper</span>
                        <span class="publication-date">2022</span>
                    </div>
                    <h3>Security in Cloud-First Enterprises</h3>
                    <p>Security frameworks and best practices for organizations adopting cloud-first strategies in their digital transformation.</p>
                    <div class="publication-tags">
                        <span class="tag">Security</span>
                        <span class="tag">Cloud</span>
                        <span class="tag">Enterprise</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Experience Section -->
    <section class="experience" id="experience">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Professional Experience</h2>
                <p class="section-subtitle">A decade of driving digital transformation and innovation</p>
            </div>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-period">2022 - Present</div>
                        <h3>Senior AI Solutions Architect</h3>
                        <h4>VIBE GUY AI</h4>
                        <p>Leading AI strategy and implementation for enterprise clients. Designed and deployed AI-powered solutions that increased operational efficiency by 45% across multiple industries.</p>
                        <ul>
                            <li>Architected AI integration frameworks for SAP environments</li>
                            <li>Led cross-functional teams of 15+ engineers and data scientists</li>
                            <li>Delivered $2M+ in cost savings through intelligent automation</li>
                        </ul>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-period">2019 - 2022</div>
                        <h3>SAP Technical Lead</h3>
                        <h4>Enterprise Solutions Inc.</h4>
                        <p>Spearheaded S/4HANA transformation projects for Fortune 500 companies. Successfully migrated 20+ legacy SAP systems to cloud-native architectures.</p>
                        <ul>
                            <li>Managed S/4HANA migrations with 99.9% uptime</li>
                            <li>Implemented DevOps practices reducing deployment time by 60%</li>
                            <li>Mentored junior developers and established best practices</li>
                        </ul>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-period">2017 - 2019</div>
                        <h3>Cloud Solutions Architect</h3>
                        <h4>Azure Consulting Partners</h4>
                        <p>Designed and implemented cloud-first architectures for mid-market enterprises. Specialized in Azure integration with existing SAP landscapes.</p>
                        <ul>
                            <li>Architected scalable cloud solutions for 50+ clients</li>
                            <li>Achieved Azure Solutions Architect Expert certification</li>
                            <li>Reduced infrastructure costs by average of 35%</li>
                        </ul>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <div class="timeline-period">2014 - 2017</div>
                        <h3>SAP Developer</h3>
                        <h4>Global Tech Solutions</h4>
                        <p>Developed custom SAP applications and integrations. Gained deep expertise in ABAP, SAP Fiori, and enterprise integration patterns.</p>
                        <ul>
                            <li>Developed 30+ custom SAP applications</li>
                            <li>Implemented real-time integration solutions</li>
                            <li>Optimized system performance by 25%</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Let's Connect</h2>
                <p class="section-subtitle">Ready to transform your enterprise with AI and SAP innovation?</p>
            </div>

            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                                <rect x="2" y="9" width="4" height="12"></rect>
                                <circle cx="4" cy="4" r="2"></circle>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h4>LinkedIn</h4>
                            <p>linkedin.com/in/amitlal-ai</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                            </svg>
                        </div>
                        <div class="contact-details">
                            <h4>GitHub</h4>
                            <p>github.com/amitlal-ai</p>
                        </div>
                    </div>
                </div>

                <div class="contact-form">
                    <form class="newsletter-form" id="newsletter-form">
                        <h3>Stay Updated</h3>
                        <p>Get the latest insights on AI, SAP, and digital transformation delivered to your inbox.</p>

                        <div class="form-group">
                            <input type="text" id="name" name="name" placeholder="Your Name" required>
                        </div>

                        <div class="form-group">
                            <input type="email" id="email" name="email" placeholder="Your Email" required>
                        </div>

                        <div class="form-group">
                            <select id="interest" name="interest" required>
                                <option value="">Select your interest</option>
                                <option value="ai">AI & Machine Learning</option>
                                <option value="sap">SAP Solutions</option>
                                <option value="cloud">Cloud Architecture</option>
                                <option value="transformation">Digital Transformation</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            Subscribe to Newsletter
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="7" y1="17" x2="17" y2="7"></line>
                                <polyline points="7,7 17,7 17,17"></polyline>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>

            <div class="social-links">
                <a href="https://linkedin.com/in/amitlal-ai" target="_blank" rel="noopener" class="social-link">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                        <rect x="2" y="9" width="4" height="12"></rect>
                        <circle cx="4" cy="4" r="2"></circle>
                    </svg>
                </a>
                <a href="https://github.com/amitlal-ai" target="_blank" rel="noopener" class="social-link">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                    </svg>
                </a>
                <a href="https://twitter.com/amitlal_ai" target="_blank" rel="noopener" class="social-link">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2024 Amit Lal - VIBE GUY AI. All rights reserved.</p>
                    <p>Transforming enterprises through AI innovation and SAP excellence.</p>
                </div>
                <div class="footer-links">
                    <a href="#privacy">Privacy Policy</a>
                    <a href="#terms">Terms of Service</a>
                    <a href="#sitemap">Sitemap</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
