{"name": "<PERSON><PERSON> - AI Engineer & SAP Expert", "short_name": "<PERSON><PERSON>", "description": "Port<PERSON>lio of Amit Lal - AI Engineer, SAP Architect, and Cloud Expert with 10+ years of experience in enterprise digital transformation.", "start_url": "/", "display": "standalone", "background_color": "#0a0a0a", "theme_color": "#0066cc", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["portfolio", "technology", "ai", "sap"], "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%230066cc'/><text x='50' y='60' font-size='40' text-anchor='middle' fill='white' font-family='Arial, sans-serif' font-weight='bold'>AL</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%230066cc'/><text x='50' y='60' font-size='40' text-anchor='middle' fill='white' font-family='Arial, sans-serif' font-weight='bold'>AL</text></svg>", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}], "shortcuts": [{"name": "View Projects", "short_name": "Projects", "description": "Browse featured projects and case studies", "url": "/#projects", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230066cc'><path d='M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z'/></svg>", "sizes": "96x96"}]}, {"name": "Read Publications", "short_name": "Publications", "description": "Explore technical articles and whitepapers", "url": "/#publications", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230066cc'><path d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z'/></svg>", "sizes": "96x96"}]}, {"name": "Contact", "short_name": "Contact", "description": "Get in touch for collaboration opportunities", "url": "/#contact", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230066cc'><path d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z'/></svg>", "sizes": "96x96"}]}], "screenshots": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 800'><rect width='1200' height='800' fill='%230a0a0a'/><text x='600' y='400' font-size='48' text-anchor='middle' fill='%230066cc' font-family='Arial, sans-serif'><PERSON>it <PERSON></text></svg>", "sizes": "1200x800", "type": "image/svg+xml", "form_factor": "wide", "label": "Desktop view of <PERSON><PERSON>'s portfolio"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 390 844'><rect width='390' height='844' fill='%230a0a0a'/><text x='195' y='422' font-size='24' text-anchor='middle' fill='%230066cc' font-family='Arial, sans-serif'>Mobile Portfolio</text></svg>", "sizes": "390x844", "type": "image/svg+xml", "form_factor": "narrow", "label": "Mobile view of <PERSON><PERSON>'s portfolio"}]}