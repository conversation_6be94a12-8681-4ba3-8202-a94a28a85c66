/* CSS Custom Properties */
:root {
  /* Colors - Dark Theme (Default) */
  --primary: #0066cc;
  --secondary: #00d4ff;
  --accent: #ff6b6b;
  --success: #4ecdc4;
  
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-glass: rgba(255, 255, 255, 0.05);
  --bg-glass-hover: rgba(255, 255, 255, 0.1);
  
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --text-muted: #666666;
  
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.3);
  
  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  --spacing-2xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius-md: 1rem;
  --radius-lg: 1.5rem;
  --radius-xl: 2rem;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Light Theme */
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --bg-glass: rgba(0, 0, 0, 0.05);
  --bg-glass-hover: rgba(0, 0, 0, 0.1);
  
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-muted: #999999;
  
  --border-color: rgba(0, 0, 0, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }

p {
  margin-bottom: var(--spacing-sm);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--secondary);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.section-title {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--spacing-sm);
}

.section-subtitle {
  color: var(--text-secondary);
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  box-shadow: 0 4px 15px rgba(0, 102, 204, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 102, 204, 0.4);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-2px);
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  z-index: var(--z-sticky);
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: var(--bg-secondary);
  box-shadow: 0 2px 20px var(--shadow-color);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-subtitle {
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-lg);
}

.nav-link {
  color: var(--text-primary);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  position: relative;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  transition: all var(--transition-fast);
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.search-btn,
.theme-toggle {
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover,
.theme-toggle:hover {
  background: var(--bg-glass);
  color: var(--primary);
}

.theme-toggle .sun-icon {
  display: none;
}

.theme-toggle .moon-icon {
  display: block;
}

[data-theme="light"] .theme-toggle .sun-icon {
  display: block;
}

[data-theme="light"] .theme-toggle .moon-icon {
  display: none;
}

.hamburger {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  transition: all var(--transition-fast);
  border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Hero Section */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: var(--spacing-2xl) 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.blob {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  animation: float 6s ease-in-out infinite;
  opacity: 0.7;
}

.blob-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.blob-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, var(--secondary), var(--accent));
  top: 60%;
  right: 20%;
  animation-delay: 2s;
}

.blob-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, var(--accent), var(--success));
  bottom: 20%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(120deg); }
  66% { transform: translateY(20px) rotate(240deg); }
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-image {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-lg);
}

.profile-img {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  border: 4px solid var(--primary);
  box-shadow: 0 0 40px rgba(0, 102, 204, 0.3);
  transition: all var(--transition-normal);
}

.profile-img:hover {
  transform: scale(1.05);
  box-shadow: 0 0 60px rgba(0, 102, 204, 0.5);
}

.image-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  z-index: -1;
  filter: blur(20px);
  opacity: 0.5;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

.hero-title {
  margin-bottom: var(--spacing-md);
}

.highlight {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: clamp(1.5rem, 3vw, 2rem);
  margin-bottom: var(--spacing-lg);
  font-family: var(--font-mono);
  color: var(--secondary);
  min-height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.1em;
}

.cursor {
  animation: blink 1s infinite;
  color: var(--primary);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-lg);
  max-width: 600px;
  margin: 0 auto;
}

.stat {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--bg-glass);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.stat:hover {
  transform: translateY(-5px);
  background: var(--bg-glass-hover);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.scroll-indicator {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid var(--primary);
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
  40% { transform: translateX(-50%) translateY(-10px); }
  60% { transform: translateX(-50%) translateY(-5px); }
}

/* About Section */
.about {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-secondary);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  align-items: start;
}

.about-intro {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.8;
}

.expertise-areas {
  display: grid;
  gap: var(--spacing-lg);
}

.expertise-item {
  padding: var(--spacing-lg);
  background: var(--bg-glass);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.expertise-item:hover {
  transform: translateY(-5px);
  background: var(--bg-glass-hover);
  box-shadow: 0 10px 30px var(--shadow-color);
}

.expertise-item h3 {
  color: var(--primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.3rem;
}

.expertise-item p {
  color: var(--text-secondary);
  margin: 0;
}

.skills-grid {
  display: grid;
  gap: var(--spacing-xl);
}

.skill-category h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-size: 1.2rem;
}

.skills {
  display: grid;
  gap: var(--spacing-md);
}

.skill-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.skill-name {
  min-width: 100px;
  font-weight: 500;
  color: var(--text-primary);
}

.skill-bar {
  flex: 1;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 4px;
  width: 0;
  transition: width 1s ease-out;
  position: relative;
}

.skill-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Search Modal */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: var(--z-modal);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 10vh;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.search-modal.active {
  opacity: 1;
  visibility: visible;
}

.search-modal-content {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  width: 90%;
  max-width: 600px;
  max-height: 70vh;
  overflow: hidden;
  box-shadow: 0 20px 60px var(--shadow-color);
  transform: translateY(-20px);
  transition: transform var(--transition-normal);
}

.search-modal.active .search-modal-content {
  transform: translateY(0);
}

.search-input-container {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  gap: var(--spacing-sm);
}

.search-input {
  flex: 1;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.1rem;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.search-close:hover {
  color: var(--text-primary);
  background: var(--bg-glass);
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    flex-direction: column;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .hamburger {
    display: flex;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .container {
    padding: 0 var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .nav-container {
    padding: var(--spacing-sm);
  }

  .hero {
    padding: var(--spacing-xl) 0;
  }

  .profile-img {
    width: 150px;
    height: 150px;
  }

  .hero-cta .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Projects Section */
.projects {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-primary);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

.project-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.project-card:hover::before {
  transform: scaleX(1);
}

.project-card:hover {
  transform: translateY(-10px);
  background: var(--bg-glass-hover);
  box-shadow: 0 20px 40px var(--shadow-color);
}

.project-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-md);
  color: white;
}

.project-card h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.4rem;
}

.project-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.tech-tag {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.85rem;
  font-weight: 500;
  border: 1px solid var(--border-color);
}

/* Publications Section */
.publications {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-secondary);
}

.publications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.publication-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.publication-card:hover {
  transform: translateY(-5px);
  background: var(--bg-glass-hover);
  box-shadow: 0 10px 25px var(--shadow-color);
}

.publication-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.publication-type {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
}

.publication-date {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.publication-card h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.2rem;
}

.publication-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.publication-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.tag {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  border: 1px solid var(--border-color);
}

/* Experience Section */
.experience {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-primary);
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--primary), var(--secondary));
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-2xl);
  padding-left: 80px;
}

.timeline-marker {
  position: absolute;
  left: 20px;
  top: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  border: 4px solid var(--bg-primary);
  z-index: 1;
}

.timeline-content {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.timeline-content:hover {
  background: var(--bg-glass-hover);
  transform: translateX(10px);
}

.timeline-period {
  color: var(--primary);
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--spacing-xs);
}

.timeline-content h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: 1.3rem;
}

.timeline-content h4 {
  color: var(--secondary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.1rem;
  font-weight: 500;
}

.timeline-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.timeline-content ul {
  list-style: none;
  padding: 0;
}

.timeline-content li {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  padding-left: var(--spacing-md);
  position: relative;
}

.timeline-content li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: var(--primary);
  font-weight: bold;
}

/* Contact Section */
.contact {
  padding: var(--spacing-2xl) 0;
  background: var(--bg-secondary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.contact-info {
  display: grid;
  gap: var(--spacing-lg);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.contact-item:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-5px);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.contact-details h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: 1.1rem;
}

.contact-details p {
  color: var(--text-secondary);
  margin: 0;
}

.contact-form {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  backdrop-filter: blur(10px);
}

.newsletter-form h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  font-size: 1.5rem;
}

.newsletter-form p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 1rem;
  transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-group input::placeholder {
  color: var(--text-muted);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.social-link {
  width: 50px;
  height: 50px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px var(--shadow-color);
}

/* Footer */
.footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-xl) 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-text p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

.footer-text p:first-child {
  color: var(--text-primary);
  font-weight: 500;
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-links a {
  color: var(--text-secondary);
  font-size: 0.9rem;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary);
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
  }

  .publications-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .timeline::before {
    left: 15px;
  }

  .timeline-item {
    padding-left: 50px;
  }

  .timeline-marker {
    left: 5px;
    width: 15px;
    height: 15px;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .social-links {
    margin-top: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .project-card {
    padding: var(--spacing-md);
  }

  .publication-card {
    padding: var(--spacing-md);
  }

  .timeline-content {
    padding: var(--spacing-md);
  }

  .contact-form {
    padding: var(--spacing-md);
  }
}

/* Animation Classes */
.animate-in {
  animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Search Results Styling */
.search-result-item {
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  border-bottom: 1px solid var(--border-color);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background: var(--bg-glass);
}

.search-result-item h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: 1rem;
}

.search-result-item p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

.search-no-results {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-muted);
}
